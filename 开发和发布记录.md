### 发布记录

pugwoo/new-api:20250722
- [sync] 同步official代码

pugwoo/new-api:20250422-3

- [del] 移除没有意义的日志：任务进度轮询开始、任务进度轮询结束
- [enhance] 渠道测试时，先清除掉响应时间

### 特别编译说明

需要至少8G内存。

### 本地开发

```bash
# 构建前端
cd one-api/web
npm install
npm run build
# 构建后端
cd ../
go mod download
go build -ldflags "-s -w" -o one-api
# 启动
chmod +x one-api
./one-api --port 3000 --log-dir ./logs
```

### 镜像打包

```bash
docker build -t pugwoo/new-api:latest .
```